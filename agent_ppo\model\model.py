#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from typing import List
import torch
from torch import nn
import math
from agent_ppo.conf.conf import Config

import sys
import os

if os.path.basename(sys.argv[0]) == "learner.py":
    import torch

    torch.set_num_interop_threads(2)
    torch.set_num_threads(2)
else:
    import torch

    torch.set_num_interop_threads(4)
    torch.set_num_threads(4)


class SelfAttentionBlock(nn.Module):
    """
    经典 Transformer Encoder block（去掉 position-wise bias，简化实现）
    """
    def __init__(self, d_model: int, n_head: int, dropout: float = 0.1):
        super().__init__()
        self.mha = nn.MultiheadAttention(d_model, n_head, dropout=dropout, batch_first=True, bias=False)
        self.norm1 = nn.LayerNorm(d_model)
        self.ffn  = nn.Sequential(
            nn.Linear(d_model, 4 * d_model),
            nn.ReLU(),
            nn.Linear(4 * d_model, d_model),
        )
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):                       # x: (B, L, d_model)
        attn_out, _ = self.mha(x, x, x)        # Self attention
        x = self.norm1(x + self.dropout(attn_out))
        ffn_out = self.dropout(self.ffn(x))           # 加 dropout
        x = self.norm2(x + ffn_out)
        return x                                # (B, L, d_model)


class NetworkModelBase(nn.Module):
    def __init__(self):
        super().__init__()
        # feature configure parameter
        # 特征配置参数
        self.data_split_shape = Config.DATA_SPLIT_SHAPE
        self.feature_split_shape = Config.FEATURE_SPLIT_SHAPE
        self.label_size = Config.ACTION_NUM
        self.feature_len = Config.FEATURE_LEN
        self.value_num = Config.VALUE_NUM

        self.var_beta = Config.BETA_START
        self.vf_coef = Config.VF_COEF

        self.clip_param = Config.CLIP_PARAM

        self.data_len = Config.data_len

        # ---------- Attention 相关超参 ----------
        self.d_model    = 64                                   # 每个 token 的维度
        self.n_head     = 4
        self.n_token    = math.ceil(self.feature_len / self.d_model)

        # (1) 先把原始一维特征投影成 token 序列
        self.feature_proj = nn.Linear(self.feature_len, self.n_token * self.d_model)
        nn.init.xavier_uniform_(self.feature_proj.weight)
        nn.init.zeros_(self.feature_proj.bias)

        # (2) 堆叠若干个 Self-Attention Block
        self.encoder = nn.Sequential(
            SelfAttentionBlock(self.d_model, self.n_head),
            SelfAttentionBlock(self.d_model, self.n_head),
        )

        # (3) 把序列展平，再走你原来的两路 MLP
        self.post_attn_dim = self.n_token * self.d_model
        self.label_mlp = MLP([self.post_attn_dim, 128, self.label_size], "label_mlp")
        self.value_mlp = MLP([self.post_attn_dim, 128, self.value_num], "value_mlp")
        self.pos_emb = nn.Parameter(torch.empty(1, self.n_token, self.d_model))
        nn.init.normal_(self.pos_emb, std=0.02)
    def process_legal_action(self, label, legal_action):
        label_max, _ = torch.max(label * legal_action, 1, True)
        label = label - label_max
        label = label * legal_action
        label = label + 1e5 * (legal_action - 1)
        return label

    def forward(self, feature, legal_action):
        # ------------- Attention 部分 -------------
        B = feature.size(0)
        x = self.feature_proj(feature)
        x = x.view(B, self.n_token, self.d_model) + self.pos_emb           # (B, L, d_model)
        x = self.encoder(x)                                  # (B, L, d_model)
        x = x.flatten(1)                                     # (B, L*d_model)

        # ------------- Head 部分（与原来一致） -----
        logits = self.label_mlp(x)                           # (B, action_num)
        logits = self.process_legal_action(logits, legal_action)
        prob   = torch.nn.functional.softmax(logits, dim=1)
        value  = self.value_mlp(x)                           # (B, value_num)
        return prob, value


class NetworkModelActor(NetworkModelBase):
    def format_data(self, obs, legal_action, device=None):
        device = device or next(self.parameters()).device
        return (torch.tensor(obs, dtype=torch.float32, device=device),
                torch.tensor(legal_action, dtype=torch.float32, device=device))


class NetworkModelLearner(NetworkModelBase):
    def format_data(self, datas):
        return datas.view(-1, self.data_len).float().split(self.data_split_shape, dim=1)

    def forward(self, data_list, inference=False):
        feature = data_list[0]
        legal_action = data_list[-1]
        return super().forward(feature, legal_action)


def make_fc_layer(in_features: int, out_features: int):
    # Wrapper function to create and initialize a linear layer
    # 创建并初始化一个线性层
    fc_layer = nn.Linear(in_features, out_features)

    # initialize weight and bias
    # 初始化权重及偏移量
    nn.init.orthogonal_(fc_layer.weight)
    nn.init.zeros_(fc_layer.bias)

    return fc_layer


class MLP(nn.Module):
    def __init__(
        self,
        fc_feat_dim_list: List[int],
        name: str,
        non_linearity: nn.Module = nn.ReLU,
        non_linearity_last: bool = False,
    ):
        # Create a MLP object
        # 创建一个 MLP 对象
        super().__init__()
        self.fc_layers = nn.Sequential()
        for i in range(len(fc_feat_dim_list) - 1):
            fc_layer = make_fc_layer(fc_feat_dim_list[i], fc_feat_dim_list[i + 1])
            self.fc_layers.add_module("{0}_fc{1}".format(name, i + 1), fc_layer)
            # no relu for the last fc layer of the mlp unless required
            # 除非有需要，否则 mlp 的最后一个 fc 层不使用 relu
            if i + 1 < len(fc_feat_dim_list) - 1 or non_linearity_last:
                self.fc_layers.add_module("{0}_non_linear{1}".format(name, i + 1), non_linearity())

    def forward(self, data):
        return self.fc_layers(data)
